#!/usr/bin/env python3
"""
Telegram Channel Bot
Handles auto-accept join requests and direct channel joining
"""

import logging
import json
import os
from datetime import datetime
from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup
from telegram.ext import (
    Application, 
    CommandHandler, 
    CallbackQueryHandler, 
    ChatJoinRequestHandler,
    MessageHandler,
    filters,
    ContextTypes
)

# Configure logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

# Bot configuration
BOT_TOKEN = "7436544351:AAGU_Mml5eH80TVqi2-U94o2zndm_3ObvD4"
DATA_FILE = "bot_data.json"

class ChannelBot:
    def __init__(self):
        self.users_data = self.load_data()
        
    def load_data(self):
        """Load user data from file"""
        try:
            if os.path.exists(DATA_FILE):
                with open(DATA_FILE, 'r') as f:
                    return json.load(f)
            return {"users": {}, "stats": {"total_joins": 0, "auto_accepts": 0}}
        except Exception as e:
            logger.error(f"Error loading data: {e}")
            return {"users": {}, "stats": {"total_joins": 0, "auto_accepts": 0}}
    
    def save_data(self):
        """Save user data to file"""
        try:
            with open(DATA_FILE, 'w') as f:
                json.dump(self.users_data, f, indent=2)
        except Exception as e:
            logger.error(f"Error saving data: {e}")
    
    def add_user(self, user_id, username, first_name, join_type="direct"):
        """Add or update user data"""
        user_id_str = str(user_id)
        self.users_data["users"][user_id_str] = {
            "username": username,
            "first_name": first_name,
            "join_date": datetime.now().isoformat(),
            "join_type": join_type,
            "last_interaction": datetime.now().isoformat()
        }
        self.users_data["stats"]["total_joins"] += 1
        if join_type == "auto_accept":
            self.users_data["stats"]["auto_accepts"] += 1
        self.save_data()

# Initialize bot instance
bot_instance = ChannelBot()

async def start(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Handle /start command"""
    user = update.effective_user
    chat = update.effective_chat
    
    # Add user to memory
    bot_instance.add_user(
        user.id, 
        user.username, 
        user.first_name,
        "direct"
    )
    
    welcome_text = f"""
🎉 Welcome {user.first_name}!

I'm your channel assistant bot. Here's what I can do:

🔹 Auto-accept join requests to the channel
🔹 Help users join the channel directly
🔹 Keep track of all members
🔹 Provide channel statistics

Use /help to see all available commands.
    """
    
    keyboard = [
        [InlineKeyboardButton("📢 Join Channel", callback_data="join_channel")],
        [InlineKeyboardButton("📊 Stats", callback_data="stats")],
        [InlineKeyboardButton("ℹ️ Help", callback_data="help")]
    ]
    reply_markup = InlineKeyboardMarkup(keyboard)
    
    await update.message.reply_text(welcome_text, reply_markup=reply_markup)

async def help_command(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Handle /help command"""
    help_text = """
🤖 **Bot Commands:**

/start - Start the bot and get welcome message
/help - Show this help message
/stats - Show channel statistics
/join - Get channel join link
/users - Show total users (admin only)

**Features:**
✅ Auto-accepts all join requests
✅ Remembers all users who interact
✅ Provides channel statistics
✅ Easy channel joining

**How it works:**
1. Users can request to join the channel
2. Bot automatically approves all requests
3. Users can also join directly using the bot
4. All interactions are logged and remembered
    """
    await update.message.reply_text(help_text, parse_mode='Markdown')

async def stats_command(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Handle /stats command"""
    stats = bot_instance.users_data["stats"]
    total_users = len(bot_instance.users_data["users"])
    
    stats_text = f"""
📊 **Channel Statistics:**

👥 Total Users: {total_users}
🔄 Total Joins: {stats['total_joins']}
✅ Auto-Accepts: {stats['auto_accepts']}
🤝 Direct Joins: {stats['total_joins'] - stats['auto_accepts']}

📅 Bot Started: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
    """
    await update.message.reply_text(stats_text, parse_mode='Markdown')

async def join_channel_command(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Handle /join command"""
    user = update.effective_user
    
    # Add user to memory
    bot_instance.add_user(
        user.id, 
        user.username, 
        user.first_name,
        "direct"
    )
    
    keyboard = [
        [InlineKeyboardButton("📢 Join Channel Now", url="https://t.me/your_channel_username")]
    ]
    reply_markup = InlineKeyboardMarkup(keyboard)
    
    join_text = f"""
🎯 Ready to join our channel, {user.first_name}?

Click the button below to join directly, or you can:
1. Search for our channel in Telegram
2. Send a join request (I'll auto-approve it!)
3. Enjoy the content!

You're now registered in our system! 🎉
    """
    
    await update.message.reply_text(join_text, reply_markup=reply_markup)

async def handle_join_request(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Auto-approve all join requests"""
    try:
        chat_join_request = update.chat_join_request
        user = chat_join_request.from_user
        chat = chat_join_request.chat
        
        # Auto-approve the request
        await context.bot.approve_chat_join_request(
            chat_id=chat.id,
            user_id=user.id
        )
        
        # Add user to memory
        bot_instance.add_user(
            user.id,
            user.username,
            user.first_name,
            "auto_accept"
        )
        
        logger.info(f"Auto-approved join request from {user.first_name} (@{user.username})")
        
        # Send welcome message to the user
        welcome_msg = f"""
🎉 Welcome to the channel, {user.first_name}!

Your join request has been automatically approved. 
Enjoy the content and feel free to interact!

Use /help to see what I can do for you.
        """
        
        try:
            await context.bot.send_message(
                chat_id=user.id,
                text=welcome_msg
            )
        except Exception as e:
            logger.warning(f"Could not send welcome message to {user.first_name}: {e}")
            
    except Exception as e:
        logger.error(f"Error handling join request: {e}")

async def button_callback(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Handle button callbacks"""
    query = update.callback_query
    await query.answer()
    
    if query.data == "join_channel":
        keyboard = [
            [InlineKeyboardButton("📢 Join Channel Now", url="https://t.me/your_channel_username")]
        ]
        reply_markup = InlineKeyboardMarkup(keyboard)
        
        await query.edit_message_text(
            "🎯 Click the button below to join our channel!",
            reply_markup=reply_markup
        )
    
    elif query.data == "stats":
        stats = bot_instance.users_data["stats"]
        total_users = len(bot_instance.users_data["users"])
        
        stats_text = f"""
📊 **Channel Statistics:**

👥 Total Users: {total_users}
🔄 Total Joins: {stats['total_joins']}
✅ Auto-Accepts: {stats['auto_accepts']}
        """
        await query.edit_message_text(stats_text, parse_mode='Markdown')
    
    elif query.data == "help":
        await query.edit_message_text(
            "Use /help command to see detailed help information!"
        )

def main():
    """Start the bot"""
    # Create application
    application = Application.builder().token(BOT_TOKEN).build()
    
    # Add handlers
    application.add_handler(CommandHandler("start", start))
    application.add_handler(CommandHandler("help", help_command))
    application.add_handler(CommandHandler("stats", stats_command))
    application.add_handler(CommandHandler("join", join_channel_command))
    application.add_handler(ChatJoinRequestHandler(handle_join_request))
    application.add_handler(CallbackQueryHandler(button_callback))
    
    # Start the bot
    logger.info("Starting Telegram Channel Bot...")
    application.run_polling(allowed_updates=Update.ALL_TYPES)

if __name__ == '__main__':
    main()
